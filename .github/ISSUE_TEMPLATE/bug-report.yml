name: Bug Report
description: File a bug report to help us improve Trae Agent
title: "[Bug]: "
labels: ["type/bug", "status/need_triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! Before reporting a bug, please make sure you have searched https://github.com/bytedance/trae-agent/issues to see if there are any existing issues that cover the same problem.

  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Please provide a clear and concise description of what the bug is.
    validations:
      required: true

  - type: textarea
    id: what-expected
    attributes:
      label: What did you expect to happen?
      description: Please provide a clear and concise description of what you expected to happen.
    validations:
      required: true

  - type: textarea
    id: traceback
    attributes:
      label: Traceback
      description: Please provide the traceback if an exception occurs.
    validations:
      required: false

  - type: textarea
    id: env-info
    attributes:
      label: What is your system, Python, dependency version?
      description: Please provide your system, Python, dependency version.
      placeholder: |
        - OS: [e.g. Ubuntu 20.04]
        - Python: [e.g. Python 3.10]
        - Dependency Version: [e.g. transformers 4.32.1]
    validations:
      required: false

  - type: textarea
    id: additional-info
    attributes:
      label: Additional information that you believe is relevant to this bug
      description: Add any other context about the problem here.
    validations:
      required: false
